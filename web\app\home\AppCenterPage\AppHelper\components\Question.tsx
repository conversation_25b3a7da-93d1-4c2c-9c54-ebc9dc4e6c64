import { COMMON } from '@/app/home/<USER>'
export default function Question() {
  return (
    <>
      <div className='w-[900px] font-[PingFangSC] text-[14px] leading-[20px] text-[#181818]'>
        <div className='pb-[20px] font-[500]'>
           问题推荐
        </div>
        <div className='flex flex-col gap-[12px]'>
          {
            COMMON.appCenterHelperQuestions.map((item, index) => {
              return (
                <div key={index} className='w-full cursor-pointer truncate rounded-[13px] bg-[#FFFFFF] px-[20px] py-[11px] font-[400]'>
                  {item.question}
                </div>
              )
          })
          }
        </div>
      </div>
    </>
  )
}
